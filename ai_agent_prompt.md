# AI Contact Research Agent - Development Specification

## Project Overview
Build an AI-powered contact research agent using <PERSON><PERSON>hai<PERSON> with a beautiful React frontend that can automatically research companies, extract contact information, and update CSV files with enriched data.

## Core Architecture

### Backend Stack
- **<PERSON><PERSON>hain** for AI agent orchestration
- **FastAPI** for REST API endpoints
- **Crawl4AI** for web scraping
- **DuckDuckGo Search** for web research
- **Pandas** for CSV processing
- **OpenRouter API** integration
- **Google Gemini API** integration

### Frontend Stack
- **React** with modern UI components
- **Tailwind CSS** for styling (similar to dashboard shown)
- **File upload with drag-and-drop**
- **Real-time progress tracking**
- **Chat interface for natural commands**

## Key Features

### 1. CSV Upload & Column Mapping
```
- Drag-and-drop CSV upload interface
- Automatic column detection
- Manual column mapping UI for:
  - Company Name (required)
  - Existing Website (optional)
  - Existing Email (optional) 
  - Existing Phone (optional)
  - Other fields to preserve
- Preview table showing first 5 rows
```

### 2. AI Research Agent
```
<PERSON><PERSON><PERSON>n Agent Tools:
- DuckDuckGoSearchTool: Find company websites and basic info
- Crawl4AITool: Extract contact information from websites
- EmailExtractorTool: Parse emails from scraped content
- PhoneExtractorTool: Parse phone numbers from scraped content
- CSVUpdaterTool: Update contact records

Agent Workflow:
1. Take company name from CSV
2. Search DuckDuckGo for "[company name] official website"
3. Extract website URL from search results
4. Use Crawl4AI to scrape website content
5. Extract emails (info@, contact@, sales@, support@, admin@)
6. Extract phone numbers (various formats)
7. Update CSV record with found information
8. Continue to next company
```

### 3. Natural Chat Interface
```
Supported Commands:
- "Research all companies"
- "Research incomplete contacts" 
- "Find missing emails for all companies"
- "Find missing phone numbers"
- "Research companies 1-50"
- "Update company [specific name]"
- "Stop research"
- "Show research progress"
- "Export updated CSV"
```

### 4. API Configuration Page
```
Settings Panel:
- OpenRouter API Key input
- Gemini API Key input
- Rate limiting settings:
  - Requests per minute (1-60)
  - Delay between requests (1-10 seconds)
- Research depth settings:
  - Quick scan vs Deep research
  - Max pages to crawl per website
- API endpoint testing buttons
```

### 5. Contact Management Interface
```
Contact View Features:
- Searchable/filterable contact table
- Status indicators (Complete/Incomplete/Researching)
- Edit individual contacts
- Bulk actions
- Export functionality
- Research progress per contact
```

## Technical Implementation

### LangChain Agent Setup
```python
from langchain.agents import create_openai_tools_agent
from langchain_community.tools import DuckDuckGoSearchRun
from langchain.tools import Tool
from crawl4ai import WebCrawler

class ContactResearchAgent:
    def __init__(self, openrouter_key, gemini_key, rate_limit=30):
        self.setup_llm(openrouter_key, gemini_key)
        self.setup_tools()
        self.rate_limit = rate_limit
        
    def setup_tools(self):
        # DuckDuckGo search tool
        # Crawl4AI web scraping tool  
        # Email extraction tool
        # Phone extraction tool
        # CSV update tool
        
    def research_company(self, company_name, existing_data):
        # Agent workflow implementation
        pass
```

## Detailed UI/UX Specifications

### Overall Design System
```
Color Palette:
- Primary: #FF6B35 (Orange/Coral)
- Secondary: #4ECDC4 (Teal accent)
- Background: #F8FAFC (Light gray)
- Cards: #FFFFFF (Pure white)
- Text Primary: #1E293B 
- Text Secondary: #64748B
- Success: #10B981
- Warning: #F59E0B
- Error: #EF4444

Typography:
- Headers: Inter/Poppins, 600-700 weight
- Body: Inter, 400-500 weight
- Code/Data: JetBrains Mono

Layout:
- Sidebar: 240px fixed width
- Main content: Fluid with max-width constraints
- Card shadows: subtle, layered depth
- Border radius: 8-12px for cards, 6px for inputs
- Spacing: 4px grid system (16px, 24px, 32px gaps)
```

### Main Dashboard Layout
```
Left Sidebar (240px width):
├── Logo & Brand (Datara style with orange icon)
├── Navigation Menu:
│   ├── 📊 Dashboard (active state with orange bg)
│   ├── 📁 Upload CSV
│   ├── 🗺️ Column Mapping  
│   ├── 💬 AI Chat
│   ├── 👥 Contacts
│   ├── ⚙️ API Config
│   └── 📤 Export
├── Research Progress Widget (bottom):
│   ├── Circular progress ring
│   ├── "45/100 Contacts Researched"
│   └── Estimated time remaining

Top Header Bar:
├── Search bar (centered, 400px width)
├── Notification bell icon
├── User avatar dropdown
└── Export button (orange, prominent)
```

### 1. Dashboard Page (Landing)
```
Hero Section:
├── Welcome message with user name
├── Quick stats cards (4-column grid):
│   ├── Total Contacts (large number + trend)
│   ├── Completed Research (percentage)
│   ├── Data Quality Score (0-100)
│   └── Last Research Run (timestamp)

Recent Activity Feed:
├── Timeline-style list of recent actions
├── "Researched 15 companies" with timestamp
├── "Updated 8 email addresses" 
└── "Exported CSV file" entries

Quick Actions Panel:
├── Large "Upload New CSV" button (orange)
├── "Continue Last Research" button
├── "Export Current Data" button
└── Recent files list (clickable)
```

### 2. CSV Upload Page
```
Upload Zone (Center of page):
├── Large dashed border area (300px height)
├── Cloud upload icon (64px, orange)
├── "Drag & drop your CSV file here"
├── "or click to browse" link
├── File requirements text (formats, size limits)
└── Recent uploads list below

Upload Success State:
├── Checkmark animation
├── File name and size display
├── Preview table (first 5 rows)
├── Column detection results
├── "Proceed to Mapping" button (orange)
└── "Upload Different File" option

File Preview Table:
├── Sticky header with column names
├── Zebra striping for rows
├── Truncated content with tooltips
├── Column type detection badges
└── Row count indicator
```

### 3. Column Mapping Interface
```
Two-Panel Layout:

Left Panel - Source CSV:
├── "Your CSV Columns" header
├── List of detected columns with icons:
│   ├── 📝 Company Name (required indicator)
│   ├── 🌐 Website (optional)
│   ├── 📧 Email (optional)
│   ├── 📞 Phone (optional)
│   └── Other columns (preservable)

Right Panel - Target Mapping:
├── "Map to Research Fields" header
├── Drag-and-drop targets:
│   ├── Company Name (red border if empty)
│   ├── Existing Website (optional)
│   ├── Existing Email (optional)
│   ├── Existing Phone (optional)
│   └── Additional fields to preserve

Visual Connections:
├── Animated lines connecting mapped fields
├── Color-coded mapping (green = mapped, red = required)
├── Auto-suggestion badges for similar names
└── "Smart Mapping" button (AI-powered)

Bottom Actions:
├── Progress indicator (step 2 of 4)
├── "Back" and "Continue to Chat" buttons
├── Mapping validation status
└── Preview of mapped data
```

### 4. AI Chat Interface
```
Chat Layout (Split screen or tabbed):

Left Side - Chat Panel:
├── Chat header with AI avatar and status
├── Message history with timestamps
├── User messages (right-aligned, blue)
├── AI responses (left-aligned, white cards)
├── Typing indicators and loading states
├── Input box with send button
└── Quick command buttons:
    ├── "Research All Companies"
    ├── "Find Missing Emails" 
    ├── "Update Incomplete Records"
    └── "Export Results"

Right Side - Live Progress:
├── Real-time research progress
├── Currently processing company name
├── Progress bars for each stage:
│   ├── Web Search (with DuckDuckGo icon)
│   ├── Website Scraping (Crawl4AI icon)
│   ├── Data Extraction (email/phone icons)
│   └── CSV Update (checkmark)
├── Recently completed items list
├── Error log (collapsible)
└── Rate limiting status indicator

Command Examples Panel:
├── Natural language examples
├── "Research the first 50 companies"
├── "Find emails for incomplete contacts"
├── "Update company Apple Inc."
└── Command syntax helper
```

### 5. Contacts Management Page
```
Header Section:
├── "Contact Database" title
├── Search bar with filters dropdown
├── View options (Table/Card/List)
├── Bulk action buttons
└── "Add Contact" button

Filter Panel (Collapsible):
├── Status filters (Complete/Incomplete/Error)
├── Date range picker
├── Has Email/Phone/Website toggles
├── Company size/industry filters
└── "Clear Filters" link

Contact Table:
├── Sortable columns with arrows
├── Status indicators (colored dots)
├── Company Name (with favicon if available)
├── Website (clickable links)
├── Email (with verification status)
├── Phone (formatted display)
├── Last Updated timestamp
├── Actions dropdown per row
└── Pagination at bottom

Contact Detail Modal:
├── Company header with logo/favicon
├── Tabbed interface:
│   ├── Contact Info (editable fields)
│   ├── Research History (timeline)
│   ├── Website Preview (iframe)
│   └── Data Sources (where info was found)
├── "Research Again" button
└── Save/Cancel actions
```

### 6. API Configuration Page
```
Configuration Cards Layout:

OpenRouter API Card:
├── Service logo and name
├── API key input (masked)
├── "Test Connection" button with status
├── Model selection dropdown
├── Usage statistics (if available)
└── Documentation link

Gemini API Card:
├── Google logo and Gemini branding
├── API key input (masked)
├── "Test Connection" button
├── Model parameters sliders
├── Rate limit settings
└── Billing/usage link

Rate Limiting Card:
├── Requests per minute slider (1-60)
├── Delay between requests (1-10 seconds)
├── Concurrent requests limit
├── "Respectful Mode" toggle
├── Current rate display
└── Advanced settings collapse

Research Settings Card:
├── Research depth radio buttons:
│   ├── Quick Scan (basic info only)
│   ├── Standard Research (recommended)
│   └── Deep Dive (comprehensive)
├── Max pages per website slider
├── Timeout settings
├── Retry attempts configuration
└── Error handling preferences

Save/Test Panel:
├── "Save Configuration" button (orange)
├── "Test All APIs" button
├── Configuration export/import
└── Reset to defaults option
```

### 7. Export & Results Page
```
Export Options Card:
├── File format selection (CSV/Excel/JSON)
├── Data filtering options:
│   ├── "Complete records only"
│   ├── "Include research metadata"
│   ├── "Date range filter"
│   └── "Custom field selection"
├── File naming template
└── "Generate Export" button

Download Section:
├── Generated files list with timestamps
├── File size and record count
├── Download buttons with progress
├── "Email to me" option
└── Auto-cleanup settings

Data Quality Report:
├── Overall completion percentage
├── Success rate by field type
├── Error summary with categories
├── Data quality score breakdown
├── Recommendations for improvement
└── Export report as PDF option

Success Metrics Dashboard:
├── Before/After comparison charts
├── Research efficiency stats
├── Time saved calculations
├── Data enhancement visualization
└── Historical performance trends
```

### Progressive Enhancement Features
```
Loading States:
├── Skeleton screens for all major components
├── Progressive loading for large datasets
├── Smooth transitions between states
├── Loading progress indicators
└── Background processing notifications

Responsive Design:
├── Mobile-first approach
├── Collapsible sidebar on mobile
├── Touch-friendly interactions
├── Swipe gestures for mobile
└── Tablet-optimized layouts

Accessibility:
├── WCAG 2.1 AA compliance
├── Keyboard navigation support
├── Screen reader optimization
├── High contrast mode support
└── Focus management
```

### Interactive Elements
```
Micro-animations:
├── Hover effects on cards and buttons
├── Progress bar animations
├── Success/error state transitions
├── Loading spinners and pulses
├── Smooth page transitions
└── Data update animations

Real-time Updates:
├── WebSocket connections for live progress
├── Push notifications for completion
├── Live data refresh without page reload
├── Collaborative features (if multi-user)
└── Background sync indicators
```

### API Endpoints
```
FastAPI Routes:
POST /upload-csv - Handle CSV file upload
POST /map-columns - Save column mappings
POST /start-research - Begin AI research process
GET /research-status - Get current progress
POST /chat-command - Handle natural language commands
GET /contacts - Retrieve contact data
PUT /contacts/{id} - Update individual contact
GET /export-csv - Download updated CSV
POST /config - Save API configurations
```

### Data Flow
```
1. User uploads CSV → Parse and preview
2. User maps columns → Store mapping configuration  
3. User gives chat command → Parse intent with LLM
4. Agent processes companies one by one:
   - Search for website
   - Scrape website content
   - Extract contact info
   - Update record
   - Respect rate limits
5. Real-time progress updates via WebSocket
6. Generate new enriched CSV
7. Display updated contacts in UI
```

### Rate Limiting & Error Handling
```
- Implement exponential backoff for API calls
- Skip companies that can't be found (log reason)
- Retry failed requests with increasing delays
- Graceful handling of blocked/restricted websites
- Progress persistence (resume if interrupted)
- Detailed logging for troubleshooting
```

### Advanced User Experience Features
```
Smart Notifications:
├── Toast notifications for actions
├── Progress notifications with dismiss
├── Error alerts with retry options
├── Success celebrations with confetti
└── Email notifications for long processes

Workflow Optimization:
├── One-click research templates
├── Batch processing with priority queues
├── Smart suggestions based on data patterns
├── Auto-save and recovery features
└── Keyboard shortcuts for power users

Data Visualization:
├── Research progress charts
├── Data quality heat maps
├── Success rate trends over time
├── Geographic distribution of contacts
└── Industry/sector analysis charts

Advanced Search & Filtering:
├── Global search across all data
├── Saved search templates
├── Advanced boolean queries
├── Fuzzy matching for company names
└── AI-powered search suggestions
```

## Detailed Component Specifications

### Navigation & Layout Components
```
Sidebar Component:
├── Collapsible with hamburger menu
├── Active state highlighting with smooth transitions
├── Icon-text pairing with proper spacing
├── Mini-mode for smaller screens
├── Search functionality within navigation
└── User context (current file, progress)

Header Component:
├── Breadcrumb navigation
├── Global search with autocomplete
├── User avatar with dropdown menu
├── Notification center with badge counts
├── Quick actions toolbar
└── Theme switcher (light/dark mode)

Main Content Area:
├── Dynamic page transitions
├── Consistent padding and margins
├── Flexible grid system
├── Sticky elements where appropriate
├── Scroll position management
└── Loading state overlays
```

### Data Interaction Components
```
Smart Table Component:
├── Virtual scrolling for large datasets
├── Column resizing and reordering
├── Multi-column sorting
├── In-line editing capabilities
├── Bulk selection with checkboxes
├── Context menus for row actions
├── Filtering headers per column
├── Export selected rows
└── Expandable row details

File Upload Component:
├── Drag-and-drop with visual feedback
├── Multiple file support
├── Progress tracking per file
├── File validation with error states
├── Thumbnail previews where applicable
├── Cancel/retry functionality
├── File type icons and metadata
└── Storage usage indicators
```

### AI Research Components
```
Research Progress Tracker:
├── Multi-stage progress visualization
├── Real-time status updates
├── Error handling with retry options
├── Pause/resume functionality
├── ETA calculations and display
├── Detailed logs with filtering
├── Performance metrics tracking
└── Resource usage monitoring

Chat Interface Component:
├── Message threading and history
├── Command auto-completion
├── Syntax highlighting for commands
├── Message reactions and feedback
├── Voice input support (future)
├── Quick reply suggestions
├── Message search and filtering
└── Export conversation history
```

### Configuration & Settings Components
```
API Configuration Panel:
├── Secure credential storage
├── Connection testing with detailed results
├── Usage monitoring and alerts
├── Rate limiting visualization
├── Health status indicators
├── Backup/restore configurations
├── Environment switching (dev/prod)
└── Integration testing workflows

Settings Management:
├── Categorized settings with search
├── Import/export preferences
├── User profile management
├── Notification preferences
├── Data retention policies
├── Security settings
├── Audit log access
└── Reset/restore options
```
```
ai-contact-agent/
├── backend/
│   ├── main.py (FastAPI app)
│   ├── agent.py (LangChain agent)
│   ├── tools.py (Custom tools)
│   ├── models.py (Data models)
│   └── config.py (Settings)
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   └── utils/
│   └── package.json
└── requirements.txt
```

## Success Criteria
- Upload CSV and map columns successfully
- Research companies via natural chat commands
- Extract websites, emails, and phone numbers
- Generate updated CSV with enriched data
- Beautiful, responsive UI matching design system
- Configurable rate limiting and API settings
- Real-time progress tracking
- Error handling and recovery

## Development Phases
1. **Phase 1**: Basic CSV upload and column mapping
2. **Phase 2**: LangChain agent with search and scraping
3. **Phase 3**: Chat interface and command processing  
4. **Phase 4**: Contact management and export features
5. **Phase 5**: API configuration and rate limiting
6. **Phase 6**: UI polish and optimization

This agent will provide a powerful, user-friendly solution for automatically enriching contact databases with minimal manual effort.