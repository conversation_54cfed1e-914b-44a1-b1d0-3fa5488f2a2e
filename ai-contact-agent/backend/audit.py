"""
Research Audit Trail System
Tracks all research activities, data sources, and changes for compliance and debugging
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass, asdict

# Local imports
from models import ContactRecord, ResearchStatus

logger = logging.getLogger(__name__)

class AuditEventType(str, Enum):
    """Types of audit events"""
    RESEARCH_STARTED = "research_started"
    RESEARCH_COMPLETED = "research_completed"
    RESEARCH_FAILED = "research_failed"
    DATA_FOUND = "data_found"
    DATA_UPDATED = "data_updated"
    SEARCH_PERFORMED = "search_performed"
    WEBSITE_SCRAPED = "website_scraped"
    EMAIL_EXTRACTED = "email_extracted"
    PHONE_EXTRACTED = "phone_extracted"
    CONFIDENCE_CALCULATED = "confidence_calculated"
    RATE_LIMITED = "rate_limited"
    ERROR_OCCURRED = "error_occurred"

@dataclass
class AuditEvent:
    """Individual audit event"""
    event_id: str
    event_type: AuditEventType
    timestamp: datetime
    company_name: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    details: Dict[str, Any] = None
    source_url: Optional[str] = None
    data_before: Optional[Dict[str, Any]] = None
    data_after: Optional[Dict[str, Any]] = None
    confidence_score: Optional[float] = None
    processing_time_ms: Optional[int] = None
    error_message: Optional[str] = None

class ResearchAuditTrail:
    """Manages audit trails for research activities"""
    
    def __init__(self, storage_path: str = "./logs/audit.jsonl"):
        self.storage_path = storage_path
        self.events: List[AuditEvent] = []
        self.current_session_id = None
        
        # Ensure log directory exists
        import os
        os.makedirs(os.path.dirname(storage_path), exist_ok=True)
    
    def start_session(self, user_id: str = None) -> str:
        """Start a new audit session"""
        import uuid
        self.current_session_id = str(uuid.uuid4())
        
        session_event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=AuditEventType.RESEARCH_STARTED,
            timestamp=datetime.now(),
            company_name="SESSION",
            user_id=user_id,
            session_id=self.current_session_id,
            details={"action": "session_started"}
        )
        
        self.log_event(session_event)
        return self.current_session_id
    
    def log_event(self, event: AuditEvent):
        """Log an audit event"""
        try:
            # Add to memory
            self.events.append(event)
            
            # Persist to file
            self._persist_event(event)
            
            logger.debug(f"Audit event logged: {event.event_type} for {event.company_name}")
            
        except Exception as e:
            logger.error(f"Failed to log audit event: {str(e)}")
    
    def log_research_start(self, company_name: str, existing_data: Dict = None):
        """Log the start of research for a company"""
        import uuid
        
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=AuditEventType.RESEARCH_STARTED,
            timestamp=datetime.now(),
            company_name=company_name,
            session_id=self.current_session_id,
            data_before=existing_data,
            details={"action": "research_initiated"}
        )
        
        self.log_event(event)
    
    def log_research_completion(self, contact: ContactRecord, processing_time_ms: int = None):
        """Log successful completion of research"""
        import uuid
        
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=AuditEventType.RESEARCH_COMPLETED,
            timestamp=datetime.now(),
            company_name=contact.company_name,
            session_id=self.current_session_id,
            data_after=contact.dict(),
            confidence_score=contact.confidence_score,
            processing_time_ms=processing_time_ms,
            details={
                "status": contact.research_status.value,
                "data_sources": contact.data_sources,
                "fields_found": {
                    "website": bool(contact.website),
                    "email": bool(contact.email),
                    "phone": bool(contact.phone)
                }
            }
        )
        
        self.log_event(event)
    
    def log_research_failure(self, company_name: str, error_message: str, processing_time_ms: int = None):
        """Log failed research attempt"""
        import uuid
        
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=AuditEventType.RESEARCH_FAILED,
            timestamp=datetime.now(),
            company_name=company_name,
            session_id=self.current_session_id,
            error_message=error_message,
            processing_time_ms=processing_time_ms,
            details={"action": "research_failed", "error": error_message}
        )
        
        self.log_event(event)
    
    def log_data_extraction(self, company_name: str, data_type: str, data_found: Any, source_url: str = None):
        """Log data extraction events"""
        import uuid
        
        event_type_map = {
            "email": AuditEventType.EMAIL_EXTRACTED,
            "phone": AuditEventType.PHONE_EXTRACTED,
            "website": AuditEventType.DATA_FOUND
        }
        
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=event_type_map.get(data_type, AuditEventType.DATA_FOUND),
            timestamp=datetime.now(),
            company_name=company_name,
            session_id=self.current_session_id,
            source_url=source_url,
            details={
                "data_type": data_type,
                "data_found": str(data_found),
                "source": source_url or "unknown"
            }
        )
        
        self.log_event(event)
    
    def log_search_activity(self, company_name: str, search_query: str, results_count: int = None):
        """Log search activities"""
        import uuid
        
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=AuditEventType.SEARCH_PERFORMED,
            timestamp=datetime.now(),
            company_name=company_name,
            session_id=self.current_session_id,
            details={
                "search_query": search_query,
                "results_count": results_count,
                "search_engine": "duckduckgo"
            }
        )
        
        self.log_event(event)
    
    def log_website_scraping(self, company_name: str, url: str, success: bool, method: str = "requests"):
        """Log website scraping activities"""
        import uuid
        
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=AuditEventType.WEBSITE_SCRAPED,
            timestamp=datetime.now(),
            company_name=company_name,
            session_id=self.current_session_id,
            source_url=url,
            details={
                "url": url,
                "success": success,
                "method": method,
                "action": "website_scraped"
            }
        )
        
        self.log_event(event)
    
    def log_rate_limiting(self, company_name: str, delay_seconds: float):
        """Log rate limiting events"""
        import uuid
        
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=AuditEventType.RATE_LIMITED,
            timestamp=datetime.now(),
            company_name=company_name,
            session_id=self.current_session_id,
            details={
                "delay_seconds": delay_seconds,
                "action": "rate_limited"
            }
        )
        
        self.log_event(event)
    
    def log_error(self, company_name: str, error_message: str, error_type: str = "general"):
        """Log error events"""
        import uuid
        
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=AuditEventType.ERROR_OCCURRED,
            timestamp=datetime.now(),
            company_name=company_name,
            session_id=self.current_session_id,
            error_message=error_message,
            details={
                "error_type": error_type,
                "error_message": error_message
            }
        )
        
        self.log_event(event)
    
    def get_session_events(self, session_id: str) -> List[AuditEvent]:
        """Get all events for a specific session"""
        return [event for event in self.events if event.session_id == session_id]
    
    def get_company_events(self, company_name: str) -> List[AuditEvent]:
        """Get all events for a specific company"""
        return [event for event in self.events if event.company_name == company_name]
    
    def get_events_by_type(self, event_type: AuditEventType) -> List[AuditEvent]:
        """Get all events of a specific type"""
        return [event for event in self.events if event.event_type == event_type]
    
    def generate_session_report(self, session_id: str) -> Dict[str, Any]:
        """Generate a comprehensive report for a session"""
        session_events = self.get_session_events(session_id)
        
        if not session_events:
            return {"error": "Session not found"}
        
        # Calculate statistics
        companies_researched = set()
        successful_research = 0
        failed_research = 0
        total_processing_time = 0
        data_sources_used = set()
        
        for event in session_events:
            if event.company_name != "SESSION":
                companies_researched.add(event.company_name)
            
            if event.event_type == AuditEventType.RESEARCH_COMPLETED:
                successful_research += 1
                if event.processing_time_ms:
                    total_processing_time += event.processing_time_ms
            elif event.event_type == AuditEventType.RESEARCH_FAILED:
                failed_research += 1
            
            if event.source_url:
                data_sources_used.add(event.source_url)
        
        return {
            "session_id": session_id,
            "start_time": min(event.timestamp for event in session_events),
            "end_time": max(event.timestamp for event in session_events),
            "total_events": len(session_events),
            "companies_researched": len(companies_researched),
            "successful_research": successful_research,
            "failed_research": failed_research,
            "success_rate": successful_research / max(successful_research + failed_research, 1) * 100,
            "average_processing_time_ms": total_processing_time / max(successful_research, 1),
            "unique_data_sources": len(data_sources_used),
            "event_breakdown": {
                event_type.value: len([e for e in session_events if e.event_type == event_type])
                for event_type in AuditEventType
            }
        }
    
    def _persist_event(self, event: AuditEvent):
        """Persist event to storage"""
        try:
            # Convert to JSON-serializable format
            event_dict = asdict(event)
            event_dict['timestamp'] = event.timestamp.isoformat()
            
            # Append to JSONL file
            with open(self.storage_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(event_dict) + '\n')
                
        except Exception as e:
            logger.error(f"Failed to persist audit event: {str(e)}")
    
    def load_events_from_storage(self, limit: int = None) -> List[AuditEvent]:
        """Load events from storage"""
        events = []
        
        try:
            with open(self.storage_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    if limit and line_num >= limit:
                        break
                    
                    try:
                        event_dict = json.loads(line.strip())
                        # Convert timestamp back to datetime
                        event_dict['timestamp'] = datetime.fromisoformat(event_dict['timestamp'])
                        event = AuditEvent(**event_dict)
                        events.append(event)
                    except Exception as e:
                        logger.warning(f"Failed to parse audit event on line {line_num}: {str(e)}")
                        continue
                        
        except FileNotFoundError:
            logger.info("No existing audit log found")
        except Exception as e:
            logger.error(f"Failed to load audit events: {str(e)}")
        
        return events
