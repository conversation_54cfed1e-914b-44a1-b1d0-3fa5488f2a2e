"""
AI Contact Research Agent using LangChain
Main agent class that orchestrates the research process
"""

import asyncio
import logging
import re
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

# LangChain imports
from langchain.agents import create_openai_tools_agent, AgentExecutor
from langchain.tools import Tool
from langchain_community.tools import DuckDuckGoSearchRun
from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema import HumanMessage, SystemMessage

# Web scraping and data processing
import pandas as pd
import requests
from bs4 import BeautifulSoup
import phonenumbers
from email_validator import validate_email, EmailNotValidError

# Local imports
from models import ContactRecord, ResearchStatus, ResearchProgress
from config import Settings, ScrapingConfig, QualityConfig

logger = logging.getLogger(__name__)

class ContactResearchAgent:
    """Main agent class for researching contact information"""
    
    def __init__(self, openrouter_key: str = None, gemini_key: str = None, rate_limit: int = 30):
        self.settings = Settings()
        self.openrouter_key = openrouter_key
        self.gemini_key = gemini_key
        self.rate_limit = rate_limit
        self.last_request_time = 0
        
        # Initialize LLM
        self.llm = None
        if openrouter_key:
            self.llm = ChatOpenAI(
                openai_api_key=openrouter_key,
                openai_api_base="https://openrouter.ai/api/v1",
                model_name="gpt-3.5-turbo",
                temperature=0.1
            )
        
        # Initialize tools
        self.search_tool = DuckDuckGoSearchRun()
        self.tools = self._setup_tools()
        
        # Initialize agent
        self.agent = None
        if self.llm:
            self.agent = self._setup_agent()
    
    def _setup_tools(self) -> List[Tool]:
        """Set up the tools for the agent"""
        tools = []
        
        # DuckDuckGo search tool
        search_tool = Tool(
            name="web_search",
            description="Search the web for company information and websites",
            func=self._search_company
        )
        tools.append(search_tool)
        
        # Website scraping tool
        scrape_tool = Tool(
            name="scrape_website",
            description="Scrape a website to extract contact information",
            func=self._scrape_website
        )
        tools.append(scrape_tool)
        
        # Email extraction tool
        email_tool = Tool(
            name="extract_emails",
            description="Extract email addresses from text content",
            func=self._extract_emails
        )
        tools.append(email_tool)
        
        # Phone extraction tool
        phone_tool = Tool(
            name="extract_phones",
            description="Extract phone numbers from text content",
            func=self._extract_phones
        )
        tools.append(phone_tool)
        
        return tools
    
    def _setup_agent(self) -> AgentExecutor:
        """Set up the LangChain agent"""
        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content="""You are an AI assistant specialized in researching company contact information.
            
Your goal is to find accurate contact information for companies including:
- Official website URL
- Contact email addresses (preferably info@, contact@, sales@)
- Phone numbers

For each company:
1. Search for the company's official website
2. Scrape the website to find contact information
3. Extract emails and phone numbers from the content
4. Return the findings in a structured format

Be thorough but respectful of rate limits and website terms of service.
Focus on finding official, publicly available contact information."""),
            MessagesPlaceholder(variable_name="chat_history"),
            HumanMessage(content="{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        agent = create_openai_tools_agent(self.llm, self.tools, prompt)
        return AgentExecutor(agent=agent, tools=self.tools, verbose=True)
    
    async def research_company(self, company_name: str, existing_data: Dict = None) -> ContactRecord:
        """Research a single company and return contact information"""
        logger.info(f"Starting research for company: {company_name}")
        
        # Rate limiting
        await self._respect_rate_limit()
        
        # Initialize contact record
        contact = ContactRecord(
            company_name=company_name,
            research_status=ResearchStatus.IN_PROGRESS,
            last_researched=datetime.now()
        )
        
        # Copy existing data if provided
        if existing_data:
            contact.website = existing_data.get('website')
            contact.email = existing_data.get('email')
            contact.phone = existing_data.get('phone')
            contact.additional_data = existing_data.get('additional_data', {})
        
        try:
            # Step 1: Search for company website if not provided
            if not contact.website:
                website = await self._find_company_website(company_name)
                if website:
                    contact.website = website
                    contact.data_sources.append("web_search")
            
            # Step 2: Scrape website for contact information
            if contact.website:
                scraped_data = await self._scrape_contact_info(contact.website)
                
                # Update contact with scraped data
                if scraped_data.get('emails') and not contact.email:
                    contact.email = scraped_data['emails'][0]  # Take first email
                    contact.data_sources.append("website_scraping")
                
                if scraped_data.get('phones') and not contact.phone:
                    contact.phone = scraped_data['phones'][0]  # Take first phone
                    contact.data_sources.append("website_scraping")
            
            # Step 3: Calculate confidence score
            contact.confidence_score = self._calculate_confidence(contact)
            
            # Mark as completed
            contact.research_status = ResearchStatus.COMPLETED
            logger.info(f"Successfully researched {company_name}")
            
        except Exception as e:
            logger.error(f"Error researching {company_name}: {str(e)}")
            contact.research_status = ResearchStatus.FAILED
            contact.research_notes = str(e)
        
        return contact
    
    async def _find_company_website(self, company_name: str) -> Optional[str]:
        """Find the official website for a company"""
        try:
            search_query = f"{company_name} official website"
            results = self.search_tool.run(search_query)
            
            # Extract URLs from search results
            urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', results)
            
            # Filter and validate URLs
            for url in urls:
                if self._is_valid_company_url(url, company_name):
                    return url
            
            return None
            
        except Exception as e:
            logger.error(f"Error finding website for {company_name}: {str(e)}")
            return None
    
    def _is_valid_company_url(self, url: str, company_name: str) -> bool:
        """Validate if URL is likely the company's official website"""
        # Basic validation
        if not url.startswith(('http://', 'https://')):
            return False
        
        # Skip social media and directory sites
        skip_domains = ['facebook.com', 'twitter.com', 'linkedin.com', 'instagram.com', 
                       'youtube.com', 'yelp.com', 'yellowpages.com', 'wikipedia.org']
        
        for skip_domain in skip_domains:
            if skip_domain in url.lower():
                return False
        
        return True
    
    async def _scrape_contact_info(self, website_url: str) -> Dict[str, List[str]]:
        """Scrape contact information from a website"""
        try:
            # Rate limiting
            await self._respect_rate_limit()
            
            headers = ScrapingConfig.HEADERS.copy()
            headers['User-Agent'] = ScrapingConfig.USER_AGENTS[0]
            
            response = requests.get(website_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            text_content = soup.get_text()
            
            # Extract emails and phones
            emails = self._extract_emails(text_content)
            phones = self._extract_phones(text_content)
            
            # Try contact pages for more information
            contact_info = {'emails': emails, 'phones': phones}
            
            for contact_path in ScrapingConfig.CONTACT_PATHS:
                try:
                    contact_url = website_url.rstrip('/') + contact_path
                    contact_response = requests.get(contact_url, headers=headers, timeout=15)
                    
                    if contact_response.status_code == 200:
                        contact_soup = BeautifulSoup(contact_response.content, 'html.parser')
                        contact_text = contact_soup.get_text()
                        
                        # Extract additional contact info
                        additional_emails = self._extract_emails(contact_text)
                        additional_phones = self._extract_phones(contact_text)
                        
                        contact_info['emails'].extend(additional_emails)
                        contact_info['phones'].extend(additional_phones)
                        
                except Exception:
                    continue  # Skip failed contact pages
            
            # Remove duplicates
            contact_info['emails'] = list(set(contact_info['emails']))
            contact_info['phones'] = list(set(contact_info['phones']))
            
            return contact_info
            
        except Exception as e:
            logger.error(f"Error scraping {website_url}: {str(e)}")
            return {'emails': [], 'phones': []}
    
    def _extract_emails(self, text: str) -> List[str]:
        """Extract email addresses from text"""
        emails = []
        
        for pattern in self.settings.email_patterns:
            found_emails = re.findall(pattern, text, re.IGNORECASE)
            emails.extend(found_emails)
        
        # Validate and filter emails
        valid_emails = []
        for email in emails:
            try:
                # Basic validation
                validated = validate_email(email)
                email_address = validated.email
                
                # Prefer business emails over personal ones
                if any(prefix in email_address.lower() for prefix in ScrapingConfig.EMAIL_PREFIXES):
                    valid_emails.insert(0, email_address)  # Prioritize business emails
                else:
                    valid_emails.append(email_address)
                    
            except EmailNotValidError:
                continue
        
        return list(dict.fromkeys(valid_emails))  # Remove duplicates while preserving order
    
    def _extract_phones(self, text: str) -> List[str]:
        """Extract phone numbers from text"""
        phones = []
        
        for pattern in self.settings.phone_patterns:
            found_phones = re.findall(pattern, text)
            phones.extend(found_phones)
        
        # Validate and format phone numbers
        valid_phones = []
        for phone in phones:
            try:
                # Parse and format phone number
                parsed = phonenumbers.parse(phone, "US")  # Default to US
                if phonenumbers.is_valid_number(parsed):
                    formatted = phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL)
                    valid_phones.append(formatted)
            except Exception:
                # If parsing fails, keep original if it looks like a phone number
                cleaned = re.sub(r'[^\d+()-.\s]', '', phone)
                if len(re.sub(r'[^\d]', '', cleaned)) >= 10:
                    valid_phones.append(cleaned.strip())
        
        return list(dict.fromkeys(valid_phones))  # Remove duplicates
    
    def _calculate_confidence(self, contact: ContactRecord) -> float:
        """Calculate confidence score for the contact data"""
        score = 0.0
        weights = QualityConfig.CONFIDENCE_WEIGHTS
        
        # Website found
        if contact.website:
            score += weights['website_found']
        
        # Email found
        if contact.email:
            score += weights['email_found']
        
        # Phone found
        if contact.phone:
            score += weights['phone_found']
        
        # Multiple data sources
        if len(contact.data_sources) > 1:
            score += weights['multiple_sources']
        
        # Official domain (if email domain matches website domain)
        if contact.email and contact.website:
            try:
                email_domain = contact.email.split('@')[1].lower()
                website_domain = contact.website.replace('https://', '').replace('http://', '').replace('www.', '').split('/')[0].lower()
                if email_domain == website_domain:
                    score += weights['official_domain']
            except Exception:
                pass
        
        return min(score, 1.0)  # Cap at 1.0
    
    async def _respect_rate_limit(self):
        """Implement rate limiting between requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        min_interval = 60.0 / self.rate_limit  # seconds between requests
        
        if time_since_last < min_interval:
            sleep_time = min_interval - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _search_company(self, query: str) -> str:
        """Wrapper for search tool"""
        return self.search_tool.run(query)
    
    def _scrape_website(self, url: str) -> str:
        """Wrapper for website scraping"""
        try:
            headers = ScrapingConfig.HEADERS.copy()
            headers['User-Agent'] = ScrapingConfig.USER_AGENTS[0]
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            return soup.get_text()
            
        except Exception as e:
            return f"Error scraping {url}: {str(e)}"
