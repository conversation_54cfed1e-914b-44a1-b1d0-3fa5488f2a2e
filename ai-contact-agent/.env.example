# AI Contact Research Agent - Environment Configuration

# API Keys (Required for full functionality)
OPENROUTER_API_KEY=sk-or-v1-your-key-here
GEMINI_API_KEY=AIza-your-key-here

# Database Configuration
DATABASE_URL=sqlite:///./contact_research.db
# For production, use PostgreSQL:
# DATABASE_URL=postgresql://user:password@localhost/contact_research

# Redis Configuration (for rate limiting and caching)
REDIS_URL=redis://localhost:6379

# Application Settings
DEBUG=True
SECRET_KEY=your-secret-key-change-in-production
APP_NAME=AI Contact Research Agent
APP_VERSION=1.0.0

# CORS Settings (adjust for production)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# File Upload Settings
MAX_FILE_SIZE=52428800  # 50MB in bytes
UPLOAD_DIR=./uploads

# Rate Limiting Defaults
DEFAULT_RATE_LIMIT=30
DEFAULT_DELAY=2.0
MAX_CONCURRENT_REQUESTS=5

# Research Settings
DEFAULT_TIMEOUT=30
MAX_RETRIES=3
MAX_PAGES_PER_SITE=3

# Crawl4AI Settings
CRAWL4AI_TIMEOUT=30
CRAWL4AI_MAX_DEPTH=2
CRAWL4AI_RESPECT_ROBOTS=True

# DuckDuckGo Search Settings
DDG_MAX_RESULTS=5
DDG_TIMEOUT=10

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# WebSocket Settings
WEBSOCKET_PING_INTERVAL=20
WEBSOCKET_PING_TIMEOUT=10

# Export Settings
EXPORT_DIR=./exports
EXPORT_RETENTION_DAYS=7

# Performance Settings
WORKER_PROCESSES=1
WORKER_CONNECTIONS=1000
