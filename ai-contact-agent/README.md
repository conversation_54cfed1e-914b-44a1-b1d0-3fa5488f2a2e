# AI Contact Research Agent

A powerful, AI-driven contact research and data enrichment system that automatically finds missing contact information for companies using advanced web scraping and LangChain agents.

## 🚀 Features

### Core Functionality
- **CSV Upload & Processing**: Drag-and-drop CSV upload with intelligent column mapping
- **AI-Powered Research**: Natural language commands to research contact information
- **Real-time Progress**: Live updates during research with WebSocket connections
- **Contact Management**: Advanced search, filtering, and bulk operations
- **Multiple Export Formats**: CSV, Excel, and JSON export options
- **API Configuration**: Easy setup for OpenRouter and Google Gemini APIs

### Advanced Features
- **Smart Column Mapping**: Automatically detect and map CSV columns to research fields
- **Confidence Scoring**: AI-generated confidence scores for research results
- **Data Quality Reports**: Comprehensive analysis of contact data completeness
- **Rate Limiting**: Configurable request throttling to respect API limits
- **Research Audit Trails**: Track data sources and research timestamps
- **Beautiful UI**: Modern, responsive interface built with Tailwind CSS

## 🛠 Tech Stack

### Backend
- **FastAPI**: High-performance Python web framework
- **LangChain**: AI agent orchestration and tool integration
- **Pydantic**: Data validation and settings management
- **WebSockets**: Real-time communication
- **SQLAlchemy**: Database ORM (ready for production scaling)

### Frontend
- **React 18**: Modern UI library with hooks
- **Vite**: Fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework
- **Heroicons**: Beautiful SVG icons
- **React Router**: Client-side routing
- **React Hot Toast**: Elegant notifications

### AI & Research Tools
- **OpenRouter API**: Access to multiple LLM models
- **Google Gemini**: Advanced content analysis
- **DuckDuckGo Search**: Web search without API keys
- **Crawl4AI**: Advanced web scraping and content extraction
- **BeautifulSoup**: HTML parsing and data extraction

## 📦 Installation

### Prerequisites
- Python 3.8+
- Node.js 16+
- npm or yarn

### Backend Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-contact-agent
   ```

2. **Create virtual environment**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

5. **Start the backend server**
   ```bash
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

The application will be available at:
- Frontend: http://localhost:5173
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## 🔧 Configuration

### API Keys

1. **OpenRouter API** (for LLM functionality)
   - Sign up at [openrouter.ai](https://openrouter.ai)
   - Get your API key
   - Add to environment: `OPENROUTER_API_KEY=sk-or-v1-...`

2. **Google Gemini API** (for advanced analysis)
   - Get API key from [Google AI Studio](https://makersuite.google.com)
   - Add to environment: `GEMINI_API_KEY=AIza...`

### Environment Variables

```bash
# API Keys
OPENROUTER_API_KEY=your_openrouter_key
GEMINI_API_KEY=your_gemini_key

# Database
DATABASE_URL=sqlite:///./contact_research.db

# Rate Limiting
DEFAULT_RATE_LIMIT=30
DEFAULT_DELAY=2.0

# Security
SECRET_KEY=your_secret_key_here
```

## 📖 Usage

### 1. Upload CSV File
- Navigate to the Upload page
- Drag and drop your CSV file or click to browse
- Preview the data and proceed to column mapping

### 2. Map Columns
- Map your CSV columns to research fields:
  - **Company Name** (required)
  - **Website** (optional)
  - **Email** (optional)
  - **Phone** (optional)
- Select additional columns to preserve

### 3. Configure APIs
- Go to API Configuration
- Add your OpenRouter and/or Gemini API keys
- Test connections and adjust rate limiting
- Configure research depth and behavior

### 4. Start Research
- Use the AI Chat interface
- Give natural language commands like:
  - "Research all companies"
  - "Find missing emails for all companies"
  - "Update companies with incomplete data"
- Monitor real-time progress

### 5. Manage Results
- View enriched contact data in the Contacts page
- Search, filter, and sort results
- Edit individual records
- Perform bulk operations

### 6. Export Data
- Choose from CSV, Excel, or JSON formats
- Select what data to include
- Download enriched contact database

## 🎯 Research Commands

The AI agent understands natural language commands:

- **"Research all companies"** - Research all uploaded companies
- **"Find missing emails"** - Focus on companies without email addresses
- **"Update incomplete records"** - Research companies with partial data
- **"Research the first 10 companies"** - Limit research to specific count
- **"Find phone numbers for tech companies"** - Conditional research

## 🔍 Data Sources

The agent searches multiple sources for contact information:

1. **Company Websites**: Official contact pages, about pages
2. **Search Results**: DuckDuckGo web search
3. **Social Media**: LinkedIn, Twitter profiles
4. **Business Directories**: Industry-specific listings
5. **News Articles**: Press releases and company mentions

## 📊 Data Quality

The system provides comprehensive data quality metrics:

- **Completion Rate**: Percentage of records with complete data
- **Confidence Scores**: AI-generated confidence for each field
- **Data Sources**: Track where information was found
- **Research Timestamps**: When data was last updated
- **Quality Reports**: Detailed analysis and recommendations

## 🚀 Deployment

### Production Setup

1. **Environment Configuration**
   ```bash
   export DEBUG=False
   export DATABASE_URL=postgresql://user:pass@localhost/dbname
   export REDIS_URL=redis://localhost:6379
   ```

2. **Database Migration**
   ```bash
   alembic upgrade head
   ```

3. **Production Server**
   ```bash
   gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

4. **Frontend Build**
   ```bash
   npm run build
   # Serve dist/ folder with nginx or similar
   ```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the [Issues](https://github.com/your-repo/issues) page
- Review the API documentation at `/docs`
- Check the progress tracking in `progress.md`

## 🔮 Roadmap

- [ ] Advanced AI models integration
- [ ] Bulk CSV processing
- [ ] API rate limiting optimization
- [ ] Advanced data validation
- [ ] Integration with CRM systems
- [ ] Scheduled research automation
- [ ] Advanced analytics dashboard

---

Built with ❤️ using FastAPI, React, and LangChain
